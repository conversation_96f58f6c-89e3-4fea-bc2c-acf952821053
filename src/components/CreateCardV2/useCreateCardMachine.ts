import { computed } from "vue";
import { useMachine } from "@xstate/vue";
import { and, assign, fromPromise, not, or, setup } from "xstate";
import type {
  TCardForIssue,
  TCardType,
  TSelectedBin,
} from "@/components/CreateCardV2/types";
import type { TCardTariffSlug } from "@/composable/Tariff/types";
import type { TPromoCodeResource } from "@/types/api/TPromoCodeResource";
import { useAccountGet } from "@/composable/API/useAccountGet";
import { useSubscriptionsInfoGet } from "@/composable/API/useSubscriptionsInfoGet";
import { useUserStore } from "@/stores/user";
import type { TCardResource } from "@/types/api/TCardResource";
import { useBrowserLocation } from "@vueuse/core";
import { useUserSpecialCondition } from "@/composable/useUserSpecialCondition";
import {
  useCountrySets,
  useSubscriptionPlusCardAutobuy1Experiment,
  useVerificationActualGet,
  useVerificationState,
} from "@/composable";
import { useVerificationAvailableGet } from "@/composable/API/useVerificationAvailableGet";
import { useNewUltimaFlowExperiment } from "@/composable/useNewUltimaFlowExperiment";

const { getIsActiveAsync } = useSubscriptionPlusCardAutobuy1Experiment();
const { getValueAsync: getNewUltimaFlowValue } = useNewUltimaFlowExperiment();

export enum CardCreateMachineState {
  INITIALIZATION = "initialization",
  SELECT_CARD_TYPE_STEP = "selectCardTypeStep",
  VERIFICATION_STEP = "verifyStep",
  SELECT_CARD_TARIFF_STEP = "selectCardTariffStep",
  SELECT_BIN_STEP = "selectBinStep",
  DEFAULT_CARD_ISSUE_STEP = "defaultCardIssueStep",
  ULTIMA_CARD_ISSUE_STEP = "ultimaWithoutSubscriptionCardIssueStep",
  ULTIMA_CARD_ISSUE_STEP_EXPERIMENT = "ultimaWithoutSubscriptionCardIssueStepExperiment",
  APPROVE_CARD_STEP_EXPERIMENT = "approveCardStepExperiment",
  AUTO_BUY_PAYMENT_STEP_EXPERIMENT = "autoBuyPaymentStepExperiment",
  SUCCESS_STEP_EXPERIMENT = "successStepExperiment",
  VERIFICATION_SCALE_STEP = "verificationScaleStep",
  APPROVE_CARD_STEP = "approveCardStep",
  AUTO_BUY_PAYMENT_STEP = "autoBuyPaymentStep",
  SUCCESS_STEP = "successStep",
}

type CardCreateMachineEvents =
  | {
      type: "xstate.done.actor.init";
      output: Pick<
        CardCreateMachineContext,
        | "isAutoBuy"
        | "isVerified"
        | "subscriptionsStatus"
        | "userSpecialCondition"
        | "numSteps"
        | "isAdvWithSubActive"
        | "lateVerification"
        | "cardType"
        | "cardTariff"
      >;
    }
  | { type: "SELECT_CARD_TYPE"; value: TCardType }
  | { type: "VERIFICATION_COMPLETED" }
  | { type: "SELECT_CARD_TARIFF"; value: TCardTariffSlug }
  | { type: "SELECT_CARD_BIN"; value: TSelectedBin }
  | { type: "ULTIMA_CARD_ISSUE"; value: TCardForIssue }
  | { type: "DEFAULT_CARD_ISSUE"; value: TCardForIssue }
  | { type: "SET_PROMO_CODE"; value: TPromoCodeResource | null }
  | { type: "SINGLE_BUY_SUCCESS"; value: TCardResource | null }
  | { type: "MULTI_BUY_SUCCESS" }
  | { type: "AUTO_BUY_SUCCESS"; value: TCardResource | null }
  | { type: "BACK" }
  | { type: "RESET" };

type CardCreateMachineContext = {
  isAutoBuy: boolean;
  isVerified: boolean;
  subscriptionsStatus: boolean;
  cardType: TCardType | null;
  cardTariff: TCardTariffSlug | null;
  selectedBin: string | null;
  cardForIssue: TCardForIssue | null;
  promoCodeData: TPromoCodeResource | null;
  resultCard: TCardResource | null;
  numSteps: number;
  step: number;
  userSpecialCondition: boolean;
  isAdvWithSubActive: boolean;
  lateVerification: boolean;
  newUltimaFlow: boolean;
};

const initialContext: CardCreateMachineContext = {
  isAutoBuy: false,
  isVerified: false,
  subscriptionsStatus: false,
  cardType: null,
  cardTariff: null,
  selectedBin: null,
  cardForIssue: null,
  promoCodeData: null,
  resultCard: null,
  numSteps: 0,
  step: 1,
  userSpecialCondition: false,
  isAdvWithSubActive: false,
  lateVerification: false,
  newUltimaFlow: false,
};

const createCardMachine = setup({
  types: {} as {
    context: CardCreateMachineContext;
    events: CardCreateMachineEvents;
  },
  actors: {
    initialize: fromPromise(async () => {
      const userStore = useUserStore();
      const [
        newUltimaFlowExperimentValue,
        { data: accountsData },
        { data: subscriptionsData },
      ] = await Promise.all([
        getNewUltimaFlowValue(),
        useAccountGet(),
        useSubscriptionsInfoGet(),
        useVerificationActualGet(),
        useVerificationAvailableGet(),
      ]);
      const subscriptionsStatus = !!subscriptionsData.value?.data?.status;
      const userHasMoney = accountsData.value?.data?.find(
        (item) => parseFloat(item.balance) > 0
      );
      const cardsCount = userStore.summary?.cards_count ?? 0;
      const userIsWarn = userStore.user.show_warn;
      const isTeamMember = userStore.isTeamMember;
      const userHasCards = cardsCount !== 0;
      const isAutoBuy =
        !userHasCards && !userHasMoney && !isTeamMember && !userIsWarn;

      // user special condition
      const userSpecialCondition =
        await useUserSpecialCondition().getSpecialCondition();

      // adv with sub experiment
      const isAdvWithSubActive = await getIsActiveAsync();

      // needVerificationAction
      const { isActive: haveNeedVerificationAction } = useCountrySets();
      const {
        isOnlyUnlimited,
        refetchData,
        isVerified: isVerifiedState,
        isVerificationAboveWelcome,
      } = useVerificationState();
      await refetchData();

      const isVerified = isVerifiedState.value;

      const lateVerification =
        (haveNeedVerificationAction.value ||
          isOnlyUnlimited.value ||
          isVerificationAboveWelcome.value) &&
        !isTeamMember;

      // type from query params
      const location = useBrowserLocation();
      const searchParams = new URLSearchParams(location.value.search);
      const params = Object.fromEntries(searchParams.entries());
      const type = params.type;

      const numSteps = getTotalSteps(
        subscriptionsStatus,
        userSpecialCondition,
        (type as TCardType) || null,
        isVerified
      );

      const newUltimaFlow = newUltimaFlowExperimentValue === 1;

      return {
        isAutoBuy,
        isVerified,
        subscriptionsStatus,
        cardType: (type as TCardType) || null,
        userSpecialCondition: userSpecialCondition,
        numSteps,
        isAdvWithSubActive,
        lateVerification,
        newUltimaFlow,
        isTeamMember,
      };
    }),
  },
  actions: {
    INIT_CONTEXT: assign(({ event }) => {
      if (event.type !== "xstate.done.actor.init") return {};
      const res = { ...event.output };
      if (event.output.cardType === "ultima") {
        res.cardTariff = "ultima-3ds";
      }
      return res;
    }),
    RESET: assign(initialContext),
    UPDATE_STEP_ON_EXIT: assign({
      step: ({ context, event }) => {
        if (event.type === "BACK") {
          return context.step - 1;
        } else {
          return context.step + 1;
        }
      },
    }),
    UPDATE_NEED_VERIFICATION_ACTION: assign({
      lateVerification: () => {
        const { isActive: haveNeedVerificationAction } = useCountrySets();
        const { isOnlyUnlimited } = useVerificationState();
        const { isTeamMember } = useUserStore();
        return (
          (haveNeedVerificationAction.value || isOnlyUnlimited.value) &&
          !isTeamMember
        );
      },
    }),
  },
  guards: {
    isUltima: ({ context }) => context.cardType === "ultima",
    isForAdv: ({ context }) => context.cardType === "forAdv",
    isSubscribed: ({ context }) => context.subscriptionsStatus,
    isVerified: ({ context }) => context.isVerified,
    // user buys first card
    isAutoBuy: ({ context }) => context.isAutoBuy,
    // additional verification step (scale) at the end of flow
    isLateVerification: ({ context }) => context.lateVerification,
    // experiment got buying adv cards with subscription
    isAdvWithSubActive: ({ context }) => context.isAdvWithSubActive,
    // If user has special condition, we skip select card type step, and go directly to select card tariff step
    isUserSpecialCondition: ({ context }) => context.userSpecialCondition,
    // new experimental design for ultima card flow
    isNewUltimaFlow: ({ context }) => context.newUltimaFlow,
  },
}).createMachine({
  id: "create-card-machine",
  initial: CardCreateMachineState.INITIALIZATION,
  context: initialContext,
  states: {
    [CardCreateMachineState.INITIALIZATION]: {
      invoke: {
        id: "init",
        src: "initialize",
        onDone: [
          {
            target: CardCreateMachineState.ULTIMA_CARD_ISSUE_STEP_EXPERIMENT,
            guard: ({ event }) => {
              return (
                event.output.newUltimaFlow &&
                (event.output.lateVerification ||
                  event.output.isVerified ||
                  event.output.isTeamMember) &&
                event.output.cardType === "ultima"
              );
            },
            actions: "INIT_CONTEXT",
          },
          {
            target: CardCreateMachineState.VERIFICATION_STEP,
            guard: ({ event }) => {
              return (
                !event.output.isVerified &&
                !event.output.lateVerification &&
                !event.output.isTeamMember
              );
            },
            actions: "INIT_CONTEXT",
          },
          {
            target: CardCreateMachineState.SELECT_CARD_TYPE_STEP,
            guard: ({ event }) => {
              return (
                event.output.cardType === null &&
                !event.output.userSpecialCondition &&
                !event.output.subscriptionsStatus
              );
            },
            actions: "INIT_CONTEXT",
          },

          {
            target: CardCreateMachineState.SELECT_CARD_TARIFF_STEP,
            guard: ({ event }) => {
              return (
                // no card type or is forAdv, and has subscription
                ([null, "forAdv"].includes(event.output.cardType) &&
                  event.output.subscriptionsStatus) ||
                // no subscription, but has adv with sub experiment, card type is forAdv or null
                (!event.output.subscriptionsStatus &&
                  event.output.isAdvWithSubActive &&
                  [null, "forAdv"].includes(event.output.cardType)) ||
                // user has special condition, and no subscription
                (event.output.userSpecialCondition &&
                  !event.output.subscriptionsStatus)
              );
            },
            actions: "INIT_CONTEXT",
          },
          {
            target: CardCreateMachineState.ULTIMA_CARD_ISSUE_STEP,
            guard: ({ event }) => {
              return (
                event.output.lateVerification ||
                event.output.isTeamMember ||
                (event.output.isVerified && event.output.cardType === "ultima")
              );
            },
            actions: "INIT_CONTEXT",
          },
        ],
      },
    },

    [CardCreateMachineState.VERIFICATION_STEP]: {
      exit: ["UPDATE_STEP_ON_EXIT", "UPDATE_NEED_VERIFICATION_ACTION"],
      on: {
        VERIFICATION_COMPLETED: [
          {
            target: CardCreateMachineState.ULTIMA_CARD_ISSUE_STEP_EXPERIMENT,
            guard: and(["isNewUltimaFlow", "isUltima"]),
            actions: assign({
              isVerified: true,
              cardTariff: "ultima-3ds",
            }),
          },
          {
            target: CardCreateMachineState.ULTIMA_CARD_ISSUE_STEP,
            guard: "isUltima",
            actions: assign({
              isVerified: true,
              cardTariff: "ultima-3ds",
            }),
          },
          {
            target: CardCreateMachineState.SELECT_CARD_TARIFF_STEP,
            guard: ({ context }) => {
              // user has special condition and no subscription
              // OR has subscription
              return (
                context.subscriptionsStatus ||
                (context.userSpecialCondition && !context.subscriptionsStatus)
              );
            },
            actions: assign({
              isVerified: true,
            }),
          },
          {
            target: CardCreateMachineState.SELECT_CARD_TYPE_STEP,
            guard: and([
              not("isUserSpecialCondition"),
              not("isSubscribed"),
              not("isLateVerification"),
            ]),
            actions: assign({
              isVerified: true,
            }),
          },
        ],
      },
    },

    [CardCreateMachineState.SELECT_CARD_TYPE_STEP]: {
      exit: "UPDATE_STEP_ON_EXIT",
      on: {
        SELECT_CARD_TYPE: [
          {
            target: CardCreateMachineState.ULTIMA_CARD_ISSUE_STEP_EXPERIMENT,
            guard: ({ event, context }) => {
              return event.value === "ultima" && context.newUltimaFlow;
            },
            actions: assign({
              cardType: ({ event }) => event.value,
              cardTariff: "ultima-3ds",
            }),
          },
          {
            target: CardCreateMachineState.ULTIMA_CARD_ISSUE_STEP,
            guard: ({ event, context }) => {
              return !context.subscriptionsStatus && event.value === "ultima";
            },
            actions: assign({
              cardType: ({ event }) => event.value,
              cardTariff: "ultima-3ds",
              numSteps: ({ context }) => (context.subscriptionsStatus ? 5 : 4),
            }),
          },
          {
            target: CardCreateMachineState.SELECT_CARD_TARIFF_STEP,
            guard: ({ event, context }) => {
              return event.value === "forAdv" && context.isAdvWithSubActive;
            },
            actions: assign({
              cardType: ({ event }) => event.value,
            }),
          },
        ],
      },
    },

    [CardCreateMachineState.SELECT_CARD_TARIFF_STEP]: {
      exit: "UPDATE_STEP_ON_EXIT",
      on: {
        SELECT_CARD_TARIFF: [
          {
            target: CardCreateMachineState.ULTIMA_CARD_ISSUE_STEP_EXPERIMENT,
            guard: ({ event, context }) => {
              return event.value === "ultima" && context.newUltimaFlow;
            },
            actions: assign({
              cardTariff: ({ event }) => event.value,
              cardType: "ultima",
              numSteps: 3,
            }),
          },
          {
            target: CardCreateMachineState.ULTIMA_CARD_ISSUE_STEP,
            guard: ({ event }) => event.value === "ultima",
            actions: assign({
              cardTariff: ({ event }) => event.value,
              cardType: "ultima",
              numSteps: 3,
            }),
          },
          {
            target: CardCreateMachineState.SELECT_BIN_STEP,
            actions: assign({
              cardTariff: ({ event }) => event.value,
              cardType: ({ event }) =>
                event.value === "ultima" ? "ultima" : "forAdv",
              numSteps: ({ context }) => {
                if (!context.subscriptionsStatus) {
                  return 4;
                } else {
                  return context.numSteps;
                }
              },
            }),
          },
        ],
      },
    },

    [CardCreateMachineState.SELECT_BIN_STEP]: {
      exit: "UPDATE_STEP_ON_EXIT",
      on: {
        SELECT_CARD_BIN: [
          {
            target: CardCreateMachineState.DEFAULT_CARD_ISSUE_STEP,
            guard: "isForAdv",
            actions: assign({
              selectedBin: ({ event }) => event.value.bin,
              cardTariff: ({ event }) => event.value.slug,
            }),
          },
        ],
        BACK: [
          {
            target: CardCreateMachineState.SELECT_CARD_TARIFF_STEP,
            actions: assign({
              cardTariff: null,
              cardType: null,
            }),
          },
        ],
      },
    },

    [CardCreateMachineState.ULTIMA_CARD_ISSUE_STEP_EXPERIMENT]: {
      exit: "UPDATE_STEP_ON_EXIT",
      on: {
        ULTIMA_CARD_ISSUE: [
          {
            target: CardCreateMachineState.VERIFICATION_SCALE_STEP,
            guard: "isLateVerification",
            actions: assign({
              cardForIssue: ({ event }) => event.value,
            }),
          },
          {
            target: CardCreateMachineState.AUTO_BUY_PAYMENT_STEP_EXPERIMENT,
            guard: and(["isNewUltimaFlow", "isAutoBuy"]),
            actions: assign({
              cardForIssue: ({ event }) => event.value,
            }),
          },
          {
            target: CardCreateMachineState.APPROVE_CARD_STEP_EXPERIMENT,
            guard: and([not("isAutoBuy"), "isNewUltimaFlow"]),
            actions: assign({
              cardForIssue: ({ event }) => event.value,
            }),
          },
        ],
        SELECT_CARD_TARIFF: {
          actions: assign({
            cardTariff: ({ event }) => event.value,
          }),
        },
        SET_PROMO_CODE: {
          actions: assign({
            promoCodeData: ({ event }) => event.value,
          }),
        },
        BACK: [
          {
            target: CardCreateMachineState.SELECT_CARD_TARIFF_STEP,
            guard: or([
              and(["isUserSpecialCondition", "isUltima"]),
              and([not("isSubscribed"), "isAdvWithSubActive"]),
            ]),
            actions: assign({
              cardTariff: null,
              promoCodeData: null,
              cardType: null,
            }),
          },
          {
            target: CardCreateMachineState.SELECT_CARD_TYPE_STEP,
            guard: and([not("isSubscribed"), not("isUserSpecialCondition")]),
            actions: assign({
              cardTariff: null,
              promoCodeData: null,
              cardType: null,
            }),
          },
        ],
      },
    },

    [CardCreateMachineState.ULTIMA_CARD_ISSUE_STEP]: {
      exit: "UPDATE_STEP_ON_EXIT",
      on: {
        ULTIMA_CARD_ISSUE: [
          {
            target: CardCreateMachineState.VERIFICATION_SCALE_STEP,
            guard: "isLateVerification",
            actions: assign({
              cardForIssue: ({ event }) => event.value,
            }),
          },
          {
            target: CardCreateMachineState.APPROVE_CARD_STEP,
            guard: not("isAutoBuy"),
            actions: assign({
              cardForIssue: ({ event }) => event.value,
            }),
          },
          {
            target: CardCreateMachineState.AUTO_BUY_PAYMENT_STEP,
            guard: "isAutoBuy",
            actions: assign({
              cardForIssue: ({ event }) => event.value,
            }),
          },
        ],
        SELECT_CARD_TARIFF: {
          actions: assign({
            cardTariff: ({ event }) => event.value,
          }),
        },
        SET_PROMO_CODE: {
          actions: assign({
            promoCodeData: ({ event }) => event.value,
          }),
        },
        BACK: [
          {
            target: CardCreateMachineState.SELECT_CARD_TARIFF_STEP,
            guard: or([
              and(["isUserSpecialCondition", "isUltima"]),
              and([not("isSubscribed"), "isAdvWithSubActive"]),
            ]),
            actions: assign({
              cardTariff: null,
              promoCodeData: null,
              cardType: null,
            }),
          },
          {
            target: CardCreateMachineState.SELECT_CARD_TYPE_STEP,
            guard: and([not("isSubscribed"), not("isUserSpecialCondition")]),
            actions: assign({
              cardTariff: null,
              promoCodeData: null,
              cardType: null,
            }),
          },
        ],
      },
    },

    [CardCreateMachineState.DEFAULT_CARD_ISSUE_STEP]: {
      exit: "UPDATE_STEP_ON_EXIT",
      on: {
        DEFAULT_CARD_ISSUE: [
          {
            target: CardCreateMachineState.VERIFICATION_SCALE_STEP,
            guard: "isLateVerification",
            actions: assign({
              cardForIssue: ({ event }) => event.value,
            }),
          },
          {
            target: CardCreateMachineState.APPROVE_CARD_STEP,
            guard: not("isAutoBuy"),
            actions: assign({
              cardForIssue: ({ event }) => event.value,
            }),
          },
          {
            target: CardCreateMachineState.AUTO_BUY_PAYMENT_STEP,
            guard: "isAutoBuy",
            actions: assign({
              cardForIssue: ({ event }) => event.value,
            }),
          },
        ],
        SET_PROMO_CODE: {
          actions: assign({
            promoCodeData: ({ event }) => event.value,
          }),
        },
        BACK: {
          target: CardCreateMachineState.SELECT_BIN_STEP,
          actions: assign({
            selectedBin: null,
            promoCodeData: null,
          }),
        },
      },
    },

    [CardCreateMachineState.VERIFICATION_SCALE_STEP]: {
      exit: "UPDATE_STEP_ON_EXIT",
      on: {
        VERIFICATION_COMPLETED: [
          {
            target: CardCreateMachineState.APPROVE_CARD_STEP_EXPERIMENT,
            guard: and([not("isAutoBuy"), "isNewUltimaFlow"]),
            actions: assign({
              isVerified: true,
              lateVerification: false,
            }),
          },
          {
            target: CardCreateMachineState.AUTO_BUY_PAYMENT_STEP_EXPERIMENT,
            guard: and(["isAutoBuy", "isNewUltimaFlow"]),
            actions: assign({
              isVerified: true,
              lateVerification: false,
            }),
          },
          {
            target: CardCreateMachineState.APPROVE_CARD_STEP,
            guard: not("isAutoBuy"),
            actions: assign({
              isVerified: true,
              lateVerification: false,
            }),
          },
          {
            target: CardCreateMachineState.AUTO_BUY_PAYMENT_STEP,
            guard: "isAutoBuy",
            actions: assign({
              isVerified: true,
              lateVerification: false,
            }),
          },
        ],
        BACK: [
          {
            target: CardCreateMachineState.DEFAULT_CARD_ISSUE_STEP,
            guard: not("isUltima"),
            actions: assign({
              cardForIssue: null,
              promoCodeData: null,
            }),
          },
          {
            target: CardCreateMachineState.ULTIMA_CARD_ISSUE_STEP_EXPERIMENT,
            guard: and(["isUltima", "isNewUltimaFlow"]),
            actions: assign({
              cardForIssue: null,
              promoCodeData: null,
            }),
          },
          {
            target: CardCreateMachineState.ULTIMA_CARD_ISSUE_STEP,
            guard: "isUltima",
            actions: assign({
              cardForIssue: null,
              promoCodeData: null,
            }),
          },
        ],
      },
    },

    [CardCreateMachineState.APPROVE_CARD_STEP]: {
      exit: "UPDATE_STEP_ON_EXIT",
      on: {
        SINGLE_BUY_SUCCESS: {
          target: CardCreateMachineState.SUCCESS_STEP,
          actions: assign({
            resultCard: ({ event }) => event.value,
          }),
        },
        MULTI_BUY_SUCCESS: {
          target: CardCreateMachineState.SUCCESS_STEP,
        },
        BACK: [
          {
            target: CardCreateMachineState.DEFAULT_CARD_ISSUE_STEP,
            guard: not("isUltima"),
            actions: assign({
              cardForIssue: null,
              promoCodeData: null,
            }),
          },
          {
            target: CardCreateMachineState.ULTIMA_CARD_ISSUE_STEP,
            guard: "isUltima",
            actions: assign({
              cardForIssue: null,
              promoCodeData: null,
            }),
          },
        ],
      },
    },

    [CardCreateMachineState.APPROVE_CARD_STEP_EXPERIMENT]: {
      exit: "UPDATE_STEP_ON_EXIT",
      on: {
        SINGLE_BUY_SUCCESS: {
          target: CardCreateMachineState.SUCCESS_STEP_EXPERIMENT,
          actions: assign({
            resultCard: ({ event }) => event.value,
          }),
        },
        MULTI_BUY_SUCCESS: {
          target: CardCreateMachineState.SUCCESS_STEP_EXPERIMENT,
        },
        BACK: [
          {
            target: CardCreateMachineState.ULTIMA_CARD_ISSUE_STEP_EXPERIMENT,
            guard: "isUltima",
            actions: assign({
              cardForIssue: null,
              promoCodeData: null,
            }),
          },
        ],
      },
    },

    [CardCreateMachineState.AUTO_BUY_PAYMENT_STEP]: {
      exit: "UPDATE_STEP_ON_EXIT",
      on: {
        AUTO_BUY_SUCCESS: {
          target: CardCreateMachineState.SUCCESS_STEP,
          actions: assign({
            resultCard: ({ event }) => event.value,
          }),
        },
        BACK: [
          {
            target: CardCreateMachineState.DEFAULT_CARD_ISSUE_STEP,
            guard: not("isUltima"),
            actions: assign({
              cardForIssue: null,
              promoCodeData: null,
            }),
          },
          {
            target: CardCreateMachineState.ULTIMA_CARD_ISSUE_STEP,
            guard: "isUltima",
            actions: assign({
              cardForIssue: null,
              promoCodeData: null,
            }),
          },
        ],
      },
    },
    [CardCreateMachineState.AUTO_BUY_PAYMENT_STEP_EXPERIMENT]: {
      exit: "UPDATE_STEP_ON_EXIT",
      on: {
        AUTO_BUY_SUCCESS: {
          target: CardCreateMachineState.SUCCESS_STEP_EXPERIMENT,
          actions: assign({
            resultCard: ({ event }) => event.value,
          }),
        },
        SET_PROMO_CODE: {
          actions: assign({
            promoCodeData: ({ event }) => event.value,
          }),
        },
        BACK: [
          {
            target: CardCreateMachineState.ULTIMA_CARD_ISSUE_STEP_EXPERIMENT,
            actions: assign({
              cardForIssue: null,
              promoCodeData: null,
            }),
          },
        ],
      },
    },

    [CardCreateMachineState.SUCCESS_STEP]: {
      entry: assign({ numSteps: 0, step: 1 }),
    },

    [CardCreateMachineState.SUCCESS_STEP_EXPERIMENT]: {
      entry: assign({ numSteps: 0, step: 0 }),
    },
  },
});

export const useCreateCardMachine = () => {
  const { send, snapshot } = useMachine(createCardMachine);
  return {
    snapshot,
    send,
    step: computed(() => snapshot.value.value),
    context: computed(() => snapshot.value.context),
  };
};

function getTotalSteps(
  subscriptionsStatus: boolean,
  userSpecialCondition: boolean,
  cardType: TCardType | null,
  isVerified: boolean
): number {
  const paths = [
    {
      stepsCount: 2,
      conditions: [
        {
          subscriptionsStatus: false,
          userSpecialCondition: false,
          cardType: "ultima",
        },
      ],
    },
    {
      stepsCount: 3,
      conditions: [
        {
          subscriptionsStatus: false,
          userSpecialCondition: true,
          cardType: null,
        },
        {
          subscriptionsStatus: true,
          userSpecialCondition: false,
          cardType: "ultima",
        },
        {
          subscriptionsStatus: false,
          userSpecialCondition: false,
          cardType: null,
        },
      ],
    },
    {
      stepsCount: 4,
      conditions: [
        {
          subscriptionsStatus: false,
          userSpecialCondition: true,
          cardType: "forAdv",
        },
        {
          subscriptionsStatus: true,
          userSpecialCondition: true,
          cardType: "ultima",
        },
        {
          subscriptionsStatus: true,
          userSpecialCondition: true,
          cardType: "forAdv",
        },
        {
          subscriptionsStatus: true,
          userSpecialCondition: false,
          cardType: "forAdv",
        },
        {
          subscriptionsStatus: true,
          userSpecialCondition: false,
          cardType: null,
        },
        {
          subscriptionsStatus: true,
          userSpecialCondition: true,
          cardType: null,
        },
      ],
    },
  ];
  let totalSteps = 0;

  for (const path of paths) {
    const match = path.conditions.some((condition) => {
      return (
        condition.subscriptionsStatus === subscriptionsStatus &&
        condition.userSpecialCondition === userSpecialCondition &&
        condition.cardType === cardType
      );
    });
    if (match) {
      totalSteps = path.stepsCount;
    }
  }
  if (!isVerified) totalSteps += 1;

  return totalSteps;
}
