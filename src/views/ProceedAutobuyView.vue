<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import CreateCardAutoBuyPayment from "@/components/CreateCardAutoBuyPaymentV2/CreateCardAutoBuyPaymentV2.vue";
import type { TCardForIssue } from "@/components/CreateCardV2/types";
import {
  type TCardTariffSlug,
  useCardAutoRefillPost,
  useCardsGet,
  usePromoCodeAttachPost,
  usePromoCodeCheckGet,
} from "@/composable";
import UIFullScreenModal from "@/components/ui/UIFullScreenModal/UIFullScreenModal.vue";
import { RouteName } from "@/constants/route_name";
import { useI18n } from "vue-i18n";
import CreateCardSuccess from "@/components/CreateCardSuccess/CreateCardSuccess.vue";
import { TPromoCodeResource } from "@/types/api/TPromoCodeResource";
import UITransition from "@/components/ui/UITransition.vue";
import Loader from "@/components/ui/Loader/Loader.vue";

const route = useRoute();
const router = useRouter();
const { t } = useI18n();

enum State {
  AUTOBUY = "autobuy",
  SUCCESS = "success",
}

const viewState = ref<State>(State.AUTOBUY);
const loading = ref(false);

const accountId = ref<number | null>(null);
const type = ref<TCardTariffSlug | null>(null);
const system = ref<number | null>(null);
const startBalance = ref<number | null>(null);
const promocode = ref<string | null>(null);
const promocodeData = ref<TPromoCodeResource | null>(null);

const cardForIssue = computed<TCardForIssue>(() => {
  return {
    bin: "",
    type: type.value!,
    startBalance: startBalance.value!,
    minValue: 1,
    accountId: accountId.value!,
    description: "",
    count: 1,
    system: system.value!,
  };
});

const enableAutoRefill = async (cardId: number) => {
  await useCardAutoRefillPost({
    card_id: cardId,
    minimum_balance: "50",
    amount_refill: "50",
  });
};

const handleAutoBuy = async () => {
  viewState.value = State.SUCCESS;
  const { data: cards } = await useCardsGet();
  const resultCard = cards.value?.data?.length ? cards.value?.data[0] : null;

  if (resultCard) {
    await enableAutoRefill(resultCard.id);
  }
};

const sendToDashboard = async () => {
  await router.push({ name: RouteName.DASHBOARD });
};

const sendToNotFound = async () => {
  await router.push({ name: RouteName.NOT_FOUND });
};

const checkAndAttachPromocode = async () => {
  if (!promocode.value && !promocode.value?.length) return;
  const { data: checkRes } = await usePromoCodeCheckGet(promocode.value);
  if (!checkRes.value?.success || !checkRes.value.data) {
    return;
  }

  const { data: attachRes } = await usePromoCodeAttachPost(promocode.value);
  if (!attachRes.value?.success) {
    return;
  }

  promocodeData.value = checkRes.value.data;
};

const extractQueryParams = () => {
  if (
    !route.query.account_id ||
    !route.query.type ||
    !route.query.system ||
    !route.query.start_balance
  ) {
    sendToNotFound();
    return;
  }
  accountId.value = parseInt(route.query.account_id?.toString());
  type.value = route.query.type?.toString() as TCardTariffSlug;
  system.value = parseInt(route.query.system?.toString());
  startBalance.value = parseInt(route.query.start_balance?.toString());
  promocode.value = route.query.code?.toString() || null;
};

onMounted(async () => {
  loading.value = true;
  extractQueryParams();

  if (promocode.value?.length) {
    await checkAndAttachPromocode();
  }
  loading.value = false;
});
</script>

<template>
  <div class="proceed-autobuy-view">
    <div>
      <UIFullScreenModal
        :is-open="true"
        :title="t('create-card.modal-title')"
        :can-go-back="false"
        @close="sendToDashboard">
        <template #content>
          <UITransition :name="'fade-slide-down'">
            <Loader v-if="loading" />
          </UITransition>
          <div class="debug">
            <pre>
              promocode: {{ promocode }}
              promocodeData: {{ promocodeData }}
              cardForIssue: {{ cardForIssue }}
            </pre>
          </div>
          <div v-if="viewState === State.AUTOBUY">
            <CreateCardAutoBuyPayment
              :card-for-issue="cardForIssue"
              :promo-code-data="promocodeData"
              @auto-buy-success="handleAutoBuy" />
          </div>
          <div v-else-if="viewState === State.SUCCESS">
            <CreateCardSuccess @confirm="sendToDashboard" />
          </div>
        </template>
      </UIFullScreenModal>
    </div>
  </div>
</template>
